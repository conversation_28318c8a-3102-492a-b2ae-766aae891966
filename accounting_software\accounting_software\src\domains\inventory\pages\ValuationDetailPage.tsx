import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  GetApp as ExportIcon,
  Print as PrintIcon,
  Assessment as ValuationIcon,
  Layers as LayersIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { inventoryService, StockValuation, StockValuationItem } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`valuation-tabpanel-${index}`}
      aria-labelledby={`valuation-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ValuationDetailPage: React.FC = () => {
  const [valuation, setValuation] = useState<StockValuation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    if (id) {
      loadValuation(parseInt(id));
    }
  }, [id]);

  const loadValuation = async (valuationId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await inventoryService.getStockValuation(valuationId);
      setValuation(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load valuation');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'IN_PROGRESS': return 'warning';
      case 'DRAFT': return 'info';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'MONTHLY': return 'Monthly';
      case 'QUARTERLY': return 'Quarterly';
      case 'ANNUAL': return 'Annual';
      case 'AD_HOC': return 'Ad-hoc';
      default: return type;
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </PageContainer>
    );
  }

  if (error || !valuation) {
    return (
      <PageContainer>
        <Alert severity="error">
          {error || 'Valuation not found'}
        </Alert>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Stock Valuation Report
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {getTypeLabel(valuation.valuation_type)} - {formatDate(valuation.valuation_date)}
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={() => window.print()}
          >
            Print
          </Button>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={() => enqueueSnackbar('Export functionality coming soon', { variant: 'info' })}
          >
            Export
          </Button>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/dashboard/inventory/valuations')}
          >
            Back
          </Button>
        </Box>
      </PageHeader>

      {/* Valuation Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Valuation Information
              </Typography>
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">Date:</Typography>
                  <Typography variant="body2">{formatDate(valuation.valuation_date)}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">Type:</Typography>
                  <Chip label={getTypeLabel(valuation.valuation_type)} size="small" variant="outlined" />
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">Status:</Typography>
                  <Chip 
                    label={valuation.status} 
                    color={getStatusColor(valuation.status) as any} 
                    size="small" 
                  />
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">Warehouse:</Typography>
                  <Typography variant="body2">{valuation.warehouse_name || 'All Warehouses'}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">Created By:</Typography>
                  <Typography variant="body2">{valuation.created_by_name}</Typography>
                </Box>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">Created:</Typography>
                  <Typography variant="body2">{formatDate(valuation.created_at)}</Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Valuation Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="primary">
                        {valuation.total_items.toLocaleString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Total Items
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="secondary">
                        {valuation.total_quantity.toLocaleString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Total Quantity
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Valuation Methods Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Valuation by Method
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="primary">
                    FIFO
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(valuation.fifo_total_value, currencyInfo)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    First In, First Out
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="secondary">
                    LIFO
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(valuation.lifo_total_value, currencyInfo)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Last In, First Out
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="success.main">
                    Average
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(valuation.average_total_value, currencyInfo)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Weighted Average
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="warning.main">
                    Standard
                  </Typography>
                  <Typography variant="h4">
                    {formatCurrency(valuation.standard_total_value, currencyInfo)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Standard Cost
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Detailed Items */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Item Details" />
            <Tab label="Summary by Category" />
          </Tabs>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          {valuation.items && valuation.items.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell>SKU</TableCell>
                    <TableCell>Warehouse</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">FIFO Value</TableCell>
                    <TableCell align="right">LIFO Value</TableCell>
                    <TableCell align="right">Average Value</TableCell>
                    <TableCell align="right">Standard Value</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {valuation.items.map((item) => (
                    <TableRow key={item.item_id} hover>
                      <TableCell>{item.product_name}</TableCell>
                      <TableCell>{item.product_sku}</TableCell>
                      <TableCell>{item.warehouse_name}</TableCell>
                      <TableCell align="right">
                        {item.quantity_on_hand.toLocaleString()}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(item.fifo_total_value, currencyInfo)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(item.lifo_total_value, currencyInfo)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(item.average_total_value, currencyInfo)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(item.standard_total_value, currencyInfo)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box textAlign="center" py={4}>
              <ValuationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No items found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This valuation report contains no items
              </Typography>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary">
              Category Summary
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Category-wise valuation summary coming soon...
            </Typography>
          </Box>
        </TabPanel>
      </Card>
    </PageContainer>
  );
};

export default ValuationDetailPage;
