import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
} from '@mui/material';
import {
  Assessment as ValuationIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  ArrowBack as BackIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { inventoryService, StockValuation, Warehouse } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface ValuationSummary {
  total_reports: number;
  latest_valuation: number;
  previous_valuation: number;
  valuation_change: number;
  valuation_change_percent: number;
  total_items_valued: number;
  average_item_value: number;
  highest_valued_warehouse: string;
  lowest_valued_warehouse: string;
}

const ValuationDashboard: React.FC = () => {
  const [valuations, setValuations] = useState<StockValuation[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [summary, setSummary] = useState<ValuationSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedWarehouse, setSelectedWarehouse] = useState('');
  const [timeRange, setTimeRange] = useState('30'); // days

  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadDashboardData();
  }, [selectedWarehouse, timeRange]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load recent valuations
      const valuationsResponse = await inventoryService.getStockValuations({
        warehouse: selectedWarehouse ? parseInt(selectedWarehouse) : undefined,
        page_size: 10,
      });
      
      // Load warehouses
      const warehousesResponse = await inventoryService.getAllWarehouses();
      
      setValuations(valuationsResponse.results);
      setWarehouses(warehousesResponse);
      
      // Calculate summary data
      calculateSummary(valuationsResponse.results);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateSummary = (valuationData: StockValuation[]) => {
    if (valuationData.length === 0) {
      setSummary(null);
      return;
    }

    const completedValuations = valuationData.filter(v => v.status === 'COMPLETED');
    
    if (completedValuations.length === 0) {
      setSummary(null);
      return;
    }

    const latest = completedValuations[0];
    const previous = completedValuations[1];
    
    const latestValue = latest.average_total_value;
    const previousValue = previous?.average_total_value || 0;
    const change = latestValue - previousValue;
    const changePercent = previousValue > 0 ? (change / previousValue) * 100 : 0;

    // Calculate warehouse values
    const warehouseValues = completedValuations.reduce((acc, val) => {
      const warehouseName = val.warehouse_name || 'All Warehouses';
      if (!acc[warehouseName]) {
        acc[warehouseName] = 0;
      }
      acc[warehouseName] += val.average_total_value;
      return acc;
    }, {} as Record<string, number>);

    const warehouseEntries = Object.entries(warehouseValues);
    const highestWarehouse = warehouseEntries.reduce((max, current) => 
      current[1] > max[1] ? current : max, warehouseEntries[0]
    );
    const lowestWarehouse = warehouseEntries.reduce((min, current) => 
      current[1] < min[1] ? current : min, warehouseEntries[0]
    );

    setSummary({
      total_reports: valuationData.length,
      latest_valuation: latestValue,
      previous_valuation: previousValue,
      valuation_change: change,
      valuation_change_percent: changePercent,
      total_items_valued: latest.total_items,
      average_item_value: latest.total_items > 0 ? latestValue / latest.total_items : 0,
      highest_valued_warehouse: highestWarehouse[0],
      lowest_valued_warehouse: lowestWarehouse[0],
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'IN_PROGRESS': return 'warning';
      case 'DRAFT': return 'info';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'MONTHLY': return 'Monthly';
      case 'QUARTERLY': return 'Quarterly';
      case 'ANNUAL': return 'Annual';
      case 'AD_HOC': return 'Ad-hoc';
      default: return type;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Valuation Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Analytics and insights for inventory valuations
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadDashboardData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/dashboard/inventory/valuations/new')}
          >
            Generate Report
          </Button>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => navigate('/dashboard/inventory/valuations')}
          >
            Back to List
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Warehouse</InputLabel>
                <Select
                  value={selectedWarehouse}
                  label="Warehouse"
                  onChange={(e) => setSelectedWarehouse(e.target.value)}
                >
                  <MenuItem value="">All Warehouses</MenuItem>
                  {warehouses.map((warehouse) => (
                    <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id.toString()}>
                      {warehouse.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Time Range</InputLabel>
                <Select
                  value={timeRange}
                  label="Time Range"
                  onChange={(e) => setTimeRange(e.target.value)}
                >
                  <MenuItem value="7">Last 7 days</MenuItem>
                  <MenuItem value="30">Last 30 days</MenuItem>
                  <MenuItem value="90">Last 90 days</MenuItem>
                  <MenuItem value="365">Last year</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Summary Cards */}
          {summary && (
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Latest Valuation
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(summary.latest_valuation, currencyInfo)}
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      {summary.valuation_change >= 0 ? (
                        <TrendingUpIcon color="success" fontSize="small" />
                      ) : (
                        <TrendingDownIcon color="error" fontSize="small" />
                      )}
                      <Typography 
                        variant="body2" 
                        color={summary.valuation_change >= 0 ? 'success.main' : 'error.main'}
                        sx={{ ml: 0.5 }}
                      >
                        {summary.valuation_change >= 0 ? '+' : ''}
                        {summary.valuation_change_percent.toFixed(1)}%
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="secondary">
                      Total Reports
                    </Typography>
                    <Typography variant="h4">
                      {summary.total_reports}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Generated reports
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="success.main">
                      Items Valued
                    </Typography>
                    <Typography variant="h4">
                      {summary.total_items_valued.toLocaleString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      In latest report
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="warning.main">
                      Avg Item Value
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(summary.average_item_value, currencyInfo)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Per inventory item
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Recent Valuations */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Valuations
              </Typography>
              <Divider sx={{ mb: 2 }} />
              {valuations.length > 0 ? (
                <Grid container spacing={2}>
                  {valuations.slice(0, 6).map((valuation) => (
                    <Grid item xs={12} sm={6} md={4} key={valuation.valuation_id}>
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          cursor: 'pointer',
                          '&:hover': { bgcolor: 'action.hover' }
                        }}
                        onClick={() => navigate(`/dashboard/inventory/valuations/${valuation.valuation_id}`)}
                      >
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                          <Typography variant="subtitle2">
                            {getTypeLabel(valuation.valuation_type)}
                          </Typography>
                          <Typography 
                            variant="caption" 
                            color={getStatusColor(valuation.status) + '.main'}
                          >
                            {valuation.status}
                          </Typography>
                        </Box>
                        <Typography variant="h6" color="primary">
                          {formatCurrency(valuation.average_total_value, currencyInfo)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {formatDate(valuation.valuation_date)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {valuation.total_items} items • {valuation.warehouse_name || 'All Warehouses'}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box textAlign="center" py={4}>
                  <ValuationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    No valuations found
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Generate your first valuation report to see analytics
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => navigate('/dashboard/inventory/valuations/new')}
                  >
                    Generate Report
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Warehouse Comparison */}
          {summary && warehouses.length > 1 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Warehouse Comparison
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle2" color="success.main">
                        Highest Valued Warehouse
                      </Typography>
                      <Typography variant="h5">
                        {summary.highest_valued_warehouse}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle2" color="warning.main">
                        Lowest Valued Warehouse
                      </Typography>
                      <Typography variant="h5">
                        {summary.lowest_valued_warehouse}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </PageContainer>
  );
};

export default ValuationDashboard;
