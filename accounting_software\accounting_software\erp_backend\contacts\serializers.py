from rest_framework import serializers
from .models import (
    Contact, Customer, Vendor, Employee
)


class ContactSerializer(serializers.ModelSerializer):
    class Meta:
        model = Contact
        fields = '__all__'


class CustomerSerializer(serializers.ModelSerializer):
    # Map frontend expected fields to model fields
    id = serializers.IntegerField(source='contact.id', read_only=True)
    customer_id = serializers.CharField(source='customer_code', read_only=True)
    display_name = serializers.CharField(source='contact.name', read_only=True)
    email = serializers.CharField(source='contact.email', read_only=True)
    phone = serializers.CharField(source='contact.phone', read_only=True)

    # Frontend expects these fields - provide defaults for missing ones
    customer_type = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    current_balance = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()
    taxable = serializers.SerializerMethodField()
    created_at = serializers.<PERSON><PERSON><PERSON><PERSON>(source='contact.created_at', read_only=True)

    # Include contact fields for backward compatibility
    contact_name = serializers.CharField(source='contact.name', read_only=True)
    contact_email = serializers.CharField(source='contact.email', read_only=True)
    contact_phone = serializers.CharField(source='contact.phone', read_only=True)
    contact_type = serializers.CharField(source='contact.contact_type', read_only=True)

    # Accept frontend form fields for creation
    displayName = serializers.CharField(write_only=True, required=False, allow_blank=True)
    firstName = serializers.CharField(write_only=True, required=False, allow_blank=True)
    lastName = serializers.CharField(write_only=True, required=False, allow_blank=True)
    companyName = serializers.CharField(write_only=True, required=False, allow_blank=True)
    email = serializers.EmailField(write_only=True, required=False, allow_blank=True)
    phone = serializers.CharField(write_only=True, required=False, allow_blank=True)
    mobile = serializers.CharField(write_only=True, required=False, allow_blank=True)
    billingAddress = serializers.DictField(write_only=True, required=False)
    shippingAddress = serializers.DictField(write_only=True, required=False)
    creditLimit = serializers.DecimalField(max_digits=15, decimal_places=2, write_only=True, required=False)
    paymentTerms = serializers.CharField(write_only=True, required=False, allow_blank=True)
    customerCategory = serializers.CharField(write_only=True, required=False, allow_blank=True)
    discountPercentage = serializers.DecimalField(max_digits=5, decimal_places=2, write_only=True, required=False)
    taxExempt = serializers.BooleanField(write_only=True, required=False)
    notes = serializers.CharField(write_only=True, required=False, allow_blank=True)
    is_active = serializers.BooleanField(write_only=True, required=False)

    class Meta:
        model = Customer
        fields = '__all__'
        extra_kwargs = {
            'contact': {'required': False}  # Don't require contact for creation
        }

    def get_customer_type(self, obj):
        """Map customer category to customer type"""
        # Check if it's a business based on company name or customer category
        if obj.company_name and obj.company_name.strip():
            return 'business'
        elif obj.customer_category == 'business':
            return 'business'
        elif obj.customer_category == 'vendor':
            return 'vendor'
        elif obj.customer_category == 'employee':
            return 'employee'
        else:
            return 'individual'

    def get_status(self, obj):
        """Default status - could be enhanced with actual status logic"""
        return 'active'

    def get_current_balance(self, obj):
        """Default balance - could be calculated from invoices/payments"""
        return 0.0

    def get_currency(self, obj):
        """Default currency"""
        return 'USD'

    def get_taxable(self, obj):
        """Inverse of tax_exempt"""
        return not obj.tax_exempt

    def validate_email(self, value):
        """Custom email validation to handle empty strings"""
        if value == '':
            return None  # Convert empty string to None
        return value

    def validate_companyName(self, value):
        """Handle empty company name"""
        return value if value else None

    def validate_phone(self, value):
        """Handle empty phone"""
        return value if value else None

    def validate_mobile(self, value):
        """Handle empty mobile"""
        return value if value else None

    def validate_paymentTerms(self, value):
        """Handle empty payment terms"""
        return value if value else None

    def validate_customerCategory(self, value):
        """Handle empty customer category"""
        return value if value else None

    def create(self, validated_data):
        """Create both Contact and Customer records from frontend form data"""
        # Extract frontend form fields
        display_name = validated_data.pop('displayName', '')
        first_name = validated_data.pop('firstName', '')
        last_name = validated_data.pop('lastName', '')
        company_name = validated_data.pop('companyName', '')
        email = validated_data.pop('email', '')
        phone = validated_data.pop('phone', '')
        mobile = validated_data.pop('mobile', '')
        billing_address = validated_data.pop('billingAddress', {})
        shipping_address = validated_data.pop('shippingAddress', {})
        credit_limit = validated_data.pop('creditLimit', None)
        payment_terms = validated_data.pop('paymentTerms', '')
        customer_category = validated_data.pop('customerCategory', '')
        discount_percentage = validated_data.pop('discountPercentage', None)
        tax_exempt = validated_data.pop('taxExempt', False)
        notes = validated_data.pop('notes', '')

        # Create Contact first
        contact_data = {
            'name': display_name or f"{first_name} {last_name}".strip(),
            'contact_type': 'customer',
            'email': email,
            'phone': phone,
            'address': f"{billing_address.get('street', '')} {billing_address.get('city', '')} {billing_address.get('state', '')} {billing_address.get('postalCode', '')}".strip(),
        }

        contact = Contact.objects.create(**contact_data)

        # Create Customer with the contact
        customer_data = {
            'contact': contact,
            'first_name': first_name,
            'last_name': last_name,
            'company_name': company_name,
            'mobile': mobile,
            'credit_limit': credit_limit,
            'payment_terms': payment_terms,
            'customer_category': customer_category,
            'discount_percentage': discount_percentage,
            'tax_exempt': tax_exempt,
            'billing_street': billing_address.get('street', ''),
            'billing_city': billing_address.get('city', ''),
            'billing_state': billing_address.get('state', ''),
            'billing_postal_code': billing_address.get('postalCode', ''),
            'billing_country': billing_address.get('country', ''),
            'shipping_same_as_billing': shipping_address.get('sameAsBilling', True),
            'shipping_street': shipping_address.get('street', ''),
            'shipping_city': shipping_address.get('city', ''),
            'shipping_state': shipping_address.get('state', ''),
            'shipping_postal_code': shipping_address.get('postalCode', ''),
            'shipping_country': shipping_address.get('country', ''),
        }

        # Define valid Customer model fields
        valid_customer_fields = {
            'contact', 'customer_code', 'credit_limit', 'payment_terms',
            'customer_category', 'discount_percentage', 'tax_exempt',
            'first_name', 'last_name', 'company_name', 'mobile',
            'billing_street', 'billing_city', 'billing_state',
            'billing_postal_code', 'billing_country',
            'shipping_same_as_billing', 'shipping_street', 'shipping_city',
            'shipping_state', 'shipping_postal_code', 'shipping_country'
        }

        # Filter customer_data to only include valid fields and non-None values
        filtered_customer_data = {
            k: v for k, v in customer_data.items()
            if k in valid_customer_fields and v is not None
        }

        customer = Customer.objects.create(**filtered_customer_data)
        return customer


class VendorSerializer(serializers.ModelSerializer):
    # Map frontend expected fields to model fields
    id = serializers.IntegerField(source='contact.id', read_only=True)
    vendor_id = serializers.CharField(source='vendor_code', read_only=True)
    display_name = serializers.CharField(source='contact.name', read_only=True)
    created_at = serializers.CharField(source='contact.created_at', read_only=True)
    updated_at = serializers.CharField(source='contact.updated_at', read_only=True)

    # Add fields for frontend form data (write-only for creation)
    displayName = serializers.CharField(write_only=True, required=False)
    firstName = serializers.CharField(write_only=True, required=False)
    lastName = serializers.CharField(write_only=True, required=False)
    companyName = serializers.CharField(write_only=True, required=False)
    billingAddress = serializers.DictField(write_only=True, required=False)
    vendorCode = serializers.CharField(write_only=True, required=False)
    leadTimeDays = serializers.IntegerField(write_only=True, required=False)
    minimumOrderAmount = serializers.DecimalField(max_digits=15, decimal_places=2, write_only=True, required=False)
    preferredVendor = serializers.BooleanField(write_only=True, required=False)
    bankDetails = serializers.CharField(write_only=True, required=False)


    # Override email and phone to be writable for creation and readable in response
    email = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()

    # Add default values for fields that don't exist in Contact/Vendor model but frontend expects
    vendor_type = serializers.SerializerMethodField()
    current_balance = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()

    class Meta:
        model = Vendor
        fields = [
            'contact', 'id', 'vendor_id', 'display_name', 'created_at', 'updated_at',
            'email', 'phone', 'vendor_type', 'current_balance', 'status', 'currency',
            'vendor_code', 'credit_limit', 'payment_terms', 'vendor_category',
            'lead_time_days', 'minimum_order_amount', 'preferred_vendor', 'bank_details',
            'first_name', 'last_name', 'company_name', 'mobile',
            'billing_street', 'billing_city', 'billing_state', 'billing_postal_code', 'billing_country',
            'shipping_same_as_billing', 'shipping_street', 'shipping_city', 'shipping_state',
            'shipping_postal_code', 'shipping_country',
            # Write-only fields for frontend form data
            'displayName', 'firstName', 'lastName', 'companyName', 'billingAddress',
            'vendorCode', 'leadTimeDays', 'minimumOrderAmount', 'preferredVendor', 'bankDetails'
        ]
        extra_kwargs = {
            'contact': {'required': False}  # We'll create the contact in the create method
        }

    def to_internal_value(self, data):
        # Allow email and phone to be passed as input fields
        internal_data = super().to_internal_value(data)
        if 'email' in data:
            internal_data['email'] = data['email']
        if 'phone' in data:
            internal_data['phone'] = data['phone']
        return internal_data

    def get_vendor_type(self, obj):
        # Map vendor_category to vendor_type expected by frontend
        category_to_type = {
            'Supplier': 'supplier',
            'Contractor': 'contractor',
            'Service': 'business',
        }
        return category_to_type.get(obj.vendor_category, 'business')

    def get_current_balance(self, obj):
        # Default to 0 for now, can be calculated from transactions later
        return 0.0

    def get_status(self, obj):
        # Default status
        return 'active'

    def get_currency(self, obj):
        # Default currency
        return 'USD'

    def get_email(self, obj):
        # Get email from contact
        return obj.contact.email if obj.contact else ''

    def get_phone(self, obj):
        # Get phone from contact
        return obj.contact.phone if obj.contact else ''

    def validate_email(self, value):
        """Custom email validation to handle empty strings"""
        if value == '':
            return None  # Convert empty string to None
        return value

    def validate_phone(self, value):
        """Handle empty phone"""
        return value if value else None

    def create(self, validated_data):
        """Create both Contact and Vendor records from frontend form data"""
        # Extract frontend form fields
        display_name = validated_data.pop('displayName', '')
        first_name = validated_data.pop('firstName', '')
        last_name = validated_data.pop('lastName', '')
        company_name = validated_data.pop('companyName', '')
        email = validated_data.pop('email_input', validated_data.pop('email', ''))
        phone = validated_data.pop('phone_input', validated_data.pop('phone', ''))
        billing_address = validated_data.pop('billingAddress', {})
        vendor_code = validated_data.pop('vendorCode', '')
        credit_limit = validated_data.pop('creditLimit', None)
        payment_terms = validated_data.pop('paymentTerms', '')
        vendor_category = validated_data.pop('vendorCategory', '')
        lead_time_days = validated_data.pop('leadTimeDays', 0)
        minimum_order_amount = validated_data.pop('minimumOrderAmount', 0)
        preferred_vendor = validated_data.pop('preferredVendor', False)
        bank_details = validated_data.pop('bankDetails', '')


        # Create Contact first
        contact_data = {
            'name': display_name or f"{first_name} {last_name}".strip(),
            'contact_type': 'vendor',
            'email': email,
            'phone': phone,
            'address': f"{billing_address.get('street', '')} {billing_address.get('city', '')} {billing_address.get('state', '')} {billing_address.get('postalCode', '')}".strip(),
        }

        contact = Contact.objects.create(**contact_data)

        # Create Vendor with the contact
        vendor_data = {
            'contact': contact,
            'vendor_code': vendor_code,
            'credit_limit': credit_limit,
            'payment_terms': payment_terms,
            'vendor_category': vendor_category,
            'lead_time_days': lead_time_days,
            'minimum_order_amount': minimum_order_amount,
            'preferred_vendor': preferred_vendor,
            'bank_details': bank_details,
            'first_name': first_name,
            'last_name': last_name,
            'company_name': company_name,
            'billing_street': billing_address.get('street', ''),
            'billing_city': billing_address.get('city', ''),
            'billing_state': billing_address.get('state', ''),
            'billing_postal_code': billing_address.get('postalCode', ''),
            'billing_country': billing_address.get('country', 'India'),
        }

        # Get valid vendor fields from the model
        valid_vendor_fields = [field.name for field in Vendor._meta.get_fields()]

        # Filter vendor_data to only include valid fields and non-None values
        filtered_vendor_data = {
            k: v for k, v in vendor_data.items()
            if k in valid_vendor_fields and v is not None
        }

        vendor = Vendor.objects.create(**filtered_vendor_data)
        return vendor


class EmployeeSerializer(serializers.ModelSerializer):
    contact_name = serializers.CharField(source='contact.name', read_only=True)
    contact_email = serializers.CharField(source='contact.email', read_only=True)
    contact_phone = serializers.CharField(source='contact.phone', read_only=True)
    contact_type = serializers.CharField(source='contact.contact_type', read_only=True)
    
    class Meta:
        model = Employee
        fields = '__all__' 