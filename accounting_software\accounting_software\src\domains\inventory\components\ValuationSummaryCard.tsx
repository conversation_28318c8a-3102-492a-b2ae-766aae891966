import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { StockValuation } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface ValuationSummaryCardProps {
  valuation: StockValuation;
  showComparison?: boolean;
  previousValuation?: StockValuation;
  onClick?: () => void;
}

const ValuationSummaryCard: React.FC<ValuationSummaryCardProps> = ({
  valuation,
  showComparison = false,
  previousValuation,
  onClick
}) => {
  const { currencyInfo } = useCurrencyInfo();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'IN_PROGRESS': return 'warning';
      case 'DRAFT': return 'info';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'MONTHLY': return 'Monthly';
      case 'QUARTERLY': return 'Quarterly';
      case 'ANNUAL': return 'Annual';
      case 'AD_HOC': return 'Ad-hoc';
      default: return type;
    }
  };

  const calculateChange = () => {
    if (!showComparison || !previousValuation) return null;
    
    const current = valuation.average_total_value;
    const previous = previousValuation.average_total_value;
    const change = current - previous;
    const changePercent = previous > 0 ? (change / previous) * 100 : 0;
    
    return { change, changePercent };
  };

  const changeData = calculateChange();

  return (
    <Card 
      sx={{ 
        cursor: onClick ? 'pointer' : 'default',
        '&:hover': onClick ? { bgcolor: 'action.hover' } : {},
        height: '100%'
      }}
      onClick={onClick}
    >
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="div">
            {getTypeLabel(valuation.valuation_type)} Valuation
          </Typography>
          <Chip 
            label={valuation.status} 
            color={getStatusColor(valuation.status) as any} 
            size="small" 
          />
        </Box>

        <Typography variant="h4" color="primary" gutterBottom>
          {formatCurrency(valuation.average_total_value, currencyInfo)}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Date
            </Typography>
            <Typography variant="body1">
              {formatDate(valuation.valuation_date)}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Items
            </Typography>
            <Typography variant="body1">
              {valuation.total_items.toLocaleString()}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body2" color="text.secondary">
              Warehouse
            </Typography>
            <Typography variant="body1">
              {valuation.warehouse_name || 'All Warehouses'}
            </Typography>
          </Grid>
        </Grid>

        {showComparison && changeData && (
          <Box mt={2} pt={2} borderTop={1} borderColor="divider">
            <Box display="flex" alignItems="center">
              {changeData.change >= 0 ? (
                <TrendingUpIcon color="success" fontSize="small" />
              ) : (
                <TrendingDownIcon color="error" fontSize="small" />
              )}
              <Typography 
                variant="body2" 
                color={changeData.change >= 0 ? 'success.main' : 'error.main'}
                sx={{ ml: 0.5 }}
              >
                {changeData.change >= 0 ? '+' : ''}
                {formatCurrency(Math.abs(changeData.change), currencyInfo)} 
                ({changeData.changePercent >= 0 ? '+' : ''}{changeData.changePercent.toFixed(1)}%)
              </Typography>
            </Box>
            <Typography variant="caption" color="text.secondary">
              vs previous valuation
            </Typography>
          </Box>
        )}

        <Box mt={2} pt={2} borderTop={1} borderColor="divider">
          <Typography variant="caption" color="text.secondary">
            Created by {valuation.created_by_name} on {formatDate(valuation.created_at)}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ValuationSummaryCard;
