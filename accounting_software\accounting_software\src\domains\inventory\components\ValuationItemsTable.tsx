import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  TableSortLabel,
  Pagination,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { formatCurrency } from '../../../shared/utils/formatters';
import { StockValuationItem } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface ValuationItemsTableProps {
  items: StockValuationItem[];
  showAllMethods?: boolean;
  defaultMethod?: 'fifo' | 'lifo' | 'average' | 'standard';
  pageSize?: number;
}

type SortField = 'product_name' | 'product_sku' | 'warehouse_name' | 'quantity_on_hand' | 'fifo_total_value' | 'lifo_total_value' | 'average_total_value' | 'standard_total_value';
type SortDirection = 'asc' | 'desc';

const ValuationItemsTable: React.FC<ValuationItemsTableProps> = ({
  items,
  showAllMethods = true,
  defaultMethod = 'average',
  pageSize = 10
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [warehouseFilter, setWarehouseFilter] = useState('');
  const [sortField, setSortField] = useState<SortField>('product_name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [page, setPage] = useState(1);

  const { currencyInfo } = useCurrencyInfo();

  // Get unique warehouses for filter
  const warehouses = Array.from(new Set(items.map(item => item.warehouse_name))).sort();

  // Filter items
  const filteredItems = items.filter(item => {
    const matchesSearch = !searchTerm || 
      item.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.product_sku.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesWarehouse = !warehouseFilter || item.warehouse_name === warehouseFilter;
    
    return matchesSearch && matchesWarehouse;
  });

  // Sort items
  const sortedItems = [...filteredItems].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Paginate items
  const totalPages = Math.ceil(sortedItems.length / pageSize);
  const startIndex = (page - 1) * pageSize;
  const paginatedItems = sortedItems.slice(startIndex, startIndex + pageSize);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getValueByMethod = (item: StockValuationItem, method: string) => {
    switch (method) {
      case 'fifo': return item.fifo_total_value;
      case 'lifo': return item.lifo_total_value;
      case 'average': return item.average_total_value;
      case 'standard': return item.standard_total_value;
      default: return item.average_total_value;
    }
  };

  const getUnitCostByMethod = (item: StockValuationItem, method: string) => {
    switch (method) {
      case 'fifo': return item.fifo_unit_cost;
      case 'lifo': return item.lifo_unit_cost;
      case 'average': return item.average_unit_cost;
      case 'standard': return item.standard_unit_cost;
      default: return item.average_unit_cost;
    }
  };

  const getHighestValueMethod = (item: StockValuationItem) => {
    const values = {
      fifo: item.fifo_total_value,
      lifo: item.lifo_total_value,
      average: item.average_total_value,
      standard: item.standard_total_value
    };
    
    return Object.entries(values).reduce((max, current) => 
      current[1] > max[1] ? current : max
    )[0];
  };

  return (
    <Box>
      {/* Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth>
            <InputLabel>Warehouse</InputLabel>
            <Select
              value={warehouseFilter}
              label="Warehouse"
              onChange={(e) => setWarehouseFilter(e.target.value)}
            >
              <MenuItem value="">All Warehouses</MenuItem>
              {warehouses.map((warehouse) => (
                <MenuItem key={warehouse} value={warehouse}>
                  {warehouse}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={12} md={5}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body2" color="text.secondary">
              Showing {paginatedItems.length} of {filteredItems.length} items
            </Typography>
            {filteredItems.length !== items.length && (
              <Chip 
                label={`${items.length - filteredItems.length} filtered`} 
                size="small" 
                variant="outlined" 
              />
            )}
          </Box>
        </Grid>
      </Grid>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'product_name'}
                  direction={sortField === 'product_name' ? sortDirection : 'asc'}
                  onClick={() => handleSort('product_name')}
                >
                  Product
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'product_sku'}
                  direction={sortField === 'product_sku' ? sortDirection : 'asc'}
                  onClick={() => handleSort('product_sku')}
                >
                  SKU
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'warehouse_name'}
                  direction={sortField === 'warehouse_name' ? sortDirection : 'asc'}
                  onClick={() => handleSort('warehouse_name')}
                >
                  Warehouse
                </TableSortLabel>
              </TableCell>
              <TableCell align="right">
                <TableSortLabel
                  active={sortField === 'quantity_on_hand'}
                  direction={sortField === 'quantity_on_hand' ? sortDirection : 'asc'}
                  onClick={() => handleSort('quantity_on_hand')}
                >
                  Quantity
                </TableSortLabel>
              </TableCell>
              {showAllMethods ? (
                <>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortField === 'fifo_total_value'}
                      direction={sortField === 'fifo_total_value' ? sortDirection : 'asc'}
                      onClick={() => handleSort('fifo_total_value')}
                    >
                      FIFO Value
                    </TableSortLabel>
                  </TableCell>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortField === 'lifo_total_value'}
                      direction={sortField === 'lifo_total_value' ? sortDirection : 'asc'}
                      onClick={() => handleSort('lifo_total_value')}
                    >
                      LIFO Value
                    </TableSortLabel>
                  </TableCell>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortField === 'average_total_value'}
                      direction={sortField === 'average_total_value' ? sortDirection : 'asc'}
                      onClick={() => handleSort('average_total_value')}
                    >
                      Average Value
                    </TableSortLabel>
                  </TableCell>
                  <TableCell align="right">
                    <TableSortLabel
                      active={sortField === 'standard_total_value'}
                      direction={sortField === 'standard_total_value' ? sortDirection : 'asc'}
                      onClick={() => handleSort('standard_total_value')}
                    >
                      Standard Value
                    </TableSortLabel>
                  </TableCell>
                </>
              ) : (
                <>
                  <TableCell align="right">Unit Cost</TableCell>
                  <TableCell align="right">Total Value</TableCell>
                  <TableCell>Best Method</TableCell>
                </>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedItems.map((item) => (
              <TableRow key={item.item_id} hover>
                <TableCell>{item.product_name}</TableCell>
                <TableCell>{item.product_sku}</TableCell>
                <TableCell>{item.warehouse_name}</TableCell>
                <TableCell align="right">
                  {item.quantity_on_hand.toLocaleString()}
                </TableCell>
                {showAllMethods ? (
                  <>
                    <TableCell align="right">
                      {formatCurrency(item.fifo_total_value, currencyInfo)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(item.lifo_total_value, currencyInfo)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(item.average_total_value, currencyInfo)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(item.standard_total_value, currencyInfo)}
                    </TableCell>
                  </>
                ) : (
                  <>
                    <TableCell align="right">
                      {formatCurrency(getUnitCostByMethod(item, defaultMethod), currencyInfo)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(getValueByMethod(item, defaultMethod), currencyInfo)}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getHighestValueMethod(item).toUpperCase()} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                    </TableCell>
                  </>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box display="flex" justifyContent="center" mt={3}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={(_, newPage) => setPage(newPage)}
            color="primary"
          />
        </Box>
      )}

      {paginatedItems.length === 0 && (
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="text.secondary">
            No items found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search or filter criteria
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ValuationItemsTable;
